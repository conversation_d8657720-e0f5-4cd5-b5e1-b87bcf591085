package proxy

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"os"
	"sync"
	"sync/atomic"

	"github.com/electricbubble/gadb"
)

type (
	// Server `TCP`服务器
	Server struct {
		*slog.Logger

		mu       sync.RWMutex
		client   *gadb.Client
		serial   string
		options  *options
		listener net.Listener

		// 连接管理
		connections map[*Socket]PlaceholderType
		connCount   *atomic.Int64

		// 控制
		ctx     context.Context
		cancel  context.CancelFunc
		started *atomic.Bool
		closed  *atomic.Bool
		done    chan PlaceholderType
	}
	ServerStats struct {
		Started           bool
		Closed            bool
		ConnectionCount   int64
		ActiveConnections int64
		MaxConnections    int64
		ListenerAddress   string
	}
)

// NewServer 创建`TCP`服务器
func NewServer(client *gadb.Client, serial string, opts ...Option) *Server {
	ctx, cancel := context.WithCancel(context.Background())

	server := &Server{
		client:  client,
		serial:  serial,
		options: defaultOptions(),

		connections: make(map[*Socket]PlaceholderType),
		connCount:   &atomic.Int64{},

		ctx:     ctx,
		cancel:  cancel,
		started: &atomic.Bool{},
		closed:  &atomic.Bool{},
		done:    make(chan PlaceholderType),
	}

	for _, opt := range opts {
		opt(server.options)
	}

	server.Logger = slog.New(
		slog.NewJSONHandler(
			os.Stdout, &slog.HandlerOptions{
				//AddSource: true,
				Level: server.options.loggerLevel,
			},
		),
	)
	return server
}

// Listen 开始监听指定地址
func (s *Server) Listen(network, address string) (err error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.started.Load() {
		return fmt.Errorf("server already started")
	}

	s.listener, err = net.Listen(network, address)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", address, err)
	}

	s.started.Store(true)

	// 启动接受连接的协程
	go s.acceptLoop()

	return nil
}

// ListenAndServe 监听并服务
func (s *Server) ListenAndServe(network, address string) error {
	if err := s.Listen(network, address); err != nil {
		return err
	}

	// 等待服务器关闭
	<-s.done
	return nil
}

// Close 关闭服务器
func (s *Server) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.closed.Load() {
		return nil
	}

	s.closed.Store(true)
	s.cancel()

	// 关闭监听器
	if s.listener != nil {
		_ = s.listener.Close()
	}

	// 关闭所有连接
	s.closeAllConnections()

	close(s.done)
	return nil
}

// CloseAllConnections 关闭所有连接但不关闭服务器
func (s *Server) CloseAllConnections() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 关闭所有连接
	s.closeAllConnections()
	return nil
}

// GetStats 获取服务器统计信息
func (s *Server) GetStats() ServerStats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	stats := ServerStats{
		Started:           s.started.Load(),
		Closed:            s.closed.Load(),
		ConnectionCount:   s.connCount.Load(),
		ActiveConnections: int64(len(s.connections)),
		MaxConnections:    s.options.maxConnections,
	}

	if s.listener != nil {
		stats.ListenerAddress = s.listener.Addr().String()
	}

	return stats
}

// acceptLoop 接受连接循环
func (s *Server) acceptLoop() {
	defer func() {
		if r := recover(); r != nil {
			s.Error("panic in accept loop", "error", r)
		}
	}()

	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			conn, err := s.listener.Accept()
			if err != nil {
				select {
				case <-s.ctx.Done():
					return // 服务器正在关闭
				default:
					s.Error("failed to accept connection", "error", err)
					continue
				}
			}

			// 检查连接数限制
			if s.connCount.Load() >= s.options.maxConnections {
				_ = conn.Close()
				s.Error("connection limit exceeded", "max_connections", s.options.maxConnections)
				continue
			}

			// 处理新连接
			go s.handleConnection(conn)
		}
	}
}

// handleConnection 处理单个连接
func (s *Server) handleConnection(conn net.Conn) {
	defer func() {
		if r := recover(); r != nil {
			s.Error("panic in connection handler", "error", r)
		}
		_ = conn.Close()
	}()

	// 创建套接字处理器
	socket := NewSocket(s.client, s.serial, conn, s.options)

	// 添加到连接列表
	s.addConnection(socket)
	defer s.removeConnection(socket)

	// 启动套接字处理
	if err := socket.Start(); err != nil {
		s.Error("failed to start socket", "error", err)
		return
	}
}

// addConnection 添加连接
func (s *Server) addConnection(socket *Socket) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.connections[socket] = Placeholder
	s.connCount.Add(1)
}

// removeConnection 移除连接
func (s *Server) removeConnection(socket *Socket) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if _, ok := s.connections[socket]; ok {
		delete(s.connections, socket)
		s.connCount.Add(-1)
	}
}

// closeAllConnections 关闭所有连接
func (s *Server) closeAllConnections() {
	for socket := range s.connections {
		_ = socket.Close()
	}
	clear(s.connections)
	s.connCount.Store(0)
}

// IsListening 检查是否正在监听
func (s *Server) IsListening() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.started.Load() && !s.closed.Load() && s.listener != nil
}

// IsClosed 检查是否已关闭
func (s *Server) IsClosed() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.closed.Load()
}

// GetAddress 获取监听地址
func (s *Server) GetAddress() net.Addr {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.listener != nil {
		return s.listener.Addr()
	}
	return nil
}
