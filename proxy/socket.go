package proxy

import (
	"crypto/rand"
	"fmt"
	"io"
	"log/slog"
	"net"
	"os"
	"sync"
	"sync/atomic"

	"github.com/electricbubble/gadb"
)

// 常量定义
const (
	UINT32Max = 0xffffffff
	UINT16Max = 0xffff

	AuthToken        = 1
	AuthSignature    = 2
	AuthRSAPublicKey = 3

	TokenLength = 20

	defaultMaxPayload = 2 << 11 // 4KB
)

// SocketError 套接字错误类型
type SocketError struct {
	Type    string
	Message string
}

func (e *SocketError) Error() string {
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// 错误类型常量
const (
	SocketErrorTypeAuth         = "AuthError"
	SocketErrorTypeUnauthorized = "UnauthorizedError"
)

// NewAuthError 创建认证错误
func NewAuthError(message string) *SocketError {
	return &SocketError{
		Type:    SocketErrorTypeAuth,
		Message: message,
	}
}

// NewUnauthorizedError 创建未授权错误
func NewUnauthorizedError() *SocketError {
	return &SocketError{
		Type:    SocketErrorTypeUnauthorized,
		Message: "Unauthorized access",
	}
}

type (
	// Socket `ADB`套接字处理器
	Socket struct {
		*slog.Logger

		mu      sync.RWMutex
		client  *gadb.Client
		serial  string
		conn    net.Conn
		options *options

		closed     bool
		reader     *PacketReader
		authorized bool
		syncToken  *RollingCounter
		remoteID   *RollingCounter
		services   *ServiceMap

		remoteAddress string
		token         []byte
		signature     []byte

		version    uint32
		maxPayload uint32

		// 统计
		packetCount *atomic.Uint64
	}
	SocketStats struct {
		RemoteAddress string
		Authorized    bool
		Version       uint32
		MaxPayload    uint32
		PacketCount   uint64
		ServiceCount  int
		Closed        bool
	}
)

// NewSocket 创建新的套接字处理器
func NewSocket(client *gadb.Client, serial string, conn net.Conn, options *options) *Socket {
	if options == nil {
		options = defaultOptions()
	}

	socket := &Socket{
		Logger: slog.New(
			slog.NewJSONHandler(
				os.Stdout, &slog.HandlerOptions{
					//AddSource: true,
					Level: options.loggerLevel,
				},
			),
		),

		client:  client,
		serial:  serial,
		conn:    conn,
		options: options,

		syncToken: NewRollingCounter(UINT32Max),
		remoteID:  NewRollingCounter(UINT32Max),
		services:  NewServiceMap(),

		remoteAddress: conn.RemoteAddr().String(),
		version:       1,
		maxPayload:    defaultMaxPayload,
		packetCount:   &atomic.Uint64{},
	}

	// 设置 TCP 选项
	if tcpConn, ok := conn.(*net.TCPConn); ok {
		_ = tcpConn.SetNoDelay(true)
	}

	// 创建数据包读取器
	socket.reader = NewPacketReader(conn)

	return socket
}

// Start 开始处理套接字，返回错误时表示处理失败
func (s *Socket) Start() error {
	for {
		packet, err := s.reader.ReadPacket()
		if err != nil {
			if err == io.EOF {
				return nil // 正常结束
			}
			return fmt.Errorf("read packet failed: %w", err)
		}

		s.packetCount.Add(1)
		if err = s.handlePacket(packet); err != nil {
			return fmt.Errorf("handle packet failed: %w", err)
		}
	}
}

// Close 关闭套接字
func (s *Socket) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.closed {
		return nil
	}

	// 结束所有服务
	_ = s.services.Close()

	// 关闭连接
	if s.conn != nil {
		_ = s.conn.Close()
	}

	s.closed = true

	return nil
}

// Write 写入数据
func (s *Socket) Write(data []byte) (int, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed || s.conn == nil {
		return 0, fmt.Errorf("socket is closed")
	}

	return s.conn.Write(data)
}

// SendPacket 发送数据包
func (s *Socket) SendPacket(packet *Packet) error {
	var data string
	if len(packet.Data) > 64 {
		data = string(packet.Data[:64]) + "..."
	} else {
		data = string(packet.Data)
	}
	s.Debug(
		"from remote",
		"command", packet.Command.String(),
		"arg0", packet.Arg0,
		"arg1", packet.Arg1,
		"length", packet.Length,
		"data", data,
	)

	_, err := s.Write(packet.ToBytes())
	return err
}

// IsClosed 检查是否已结束
func (s *Socket) IsClosed() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.closed
}

// GetStats 获取统计信息
func (s *Socket) GetStats() SocketStats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return SocketStats{
		RemoteAddress: s.remoteAddress,
		Authorized:    s.authorized,
		Version:       s.version,
		MaxPayload:    s.maxPayload,
		PacketCount:   s.packetCount.Load(),
		ServiceCount:  s.services.Count(),
		Closed:        s.closed,
	}
}

// handlePacket 处理数据包
func (s *Socket) handlePacket(packet *Packet) error {
	if s.closed {
		return nil
	}

	var data string
	if len(packet.Data) > 64 {
		data = string(packet.Data[:64]) + "..."
	} else {
		data = string(packet.Data)
	}
	s.Debug(
		"to remote",
		"command", packet.Command.String(),
		"arg0", packet.Arg0,
		"arg1", packet.Arg1,
		"length", packet.Length,
		"data", data,
	)

	switch packet.Command {
	case CommandOfSYNC:
		return s.handleSyncPacket(packet)
	case CommandOfCNXN:
		return s.handleConnectionPacket(packet)
	case CommandOfOPEN:
		return s.handleOpenPacket(packet)
	case CommandOfOKAY, CommandOfWRTE, CommandOfCLSE:
		return s.forwardServicePacket(packet)
	case CommandOfAUTH:
		return s.handleAuthPacket(packet)
	default:
		return fmt.Errorf("unknown command: 0x%08x", packet.Command)
	}
}

// handleSyncPacket 处理同步数据包
func (s *Socket) handleSyncPacket(_ *Packet) error {
	return s.SendPacket(NewPacket(CommandOfSYNC, 1, s.syncToken.Next(), nil))
}

// handleConnectionPacket 处理连接数据包
func (s *Socket) handleConnectionPacket(packet *Packet) (err error) {
	s.version = Swap32(packet.Arg0)
	s.maxPayload = min(UINT16Max, packet.Arg1)

	// 创建认证令牌
	s.token, err = s.createToken()
	if err != nil {
		return fmt.Errorf("failed to create token: %w", err)
	}

	// 发送认证请求
	return s.SendPacket(NewPacket(CommandOfAUTH, AuthToken, 0, s.token))
}

// createToken 创建认证令牌
func (s *Socket) createToken() ([]byte, error) {
	token := make([]byte, TokenLength)
	_, err := rand.Read(token)
	return token, err
}

// skipNull 跳过末尾的空字节
func (s *Socket) skipNull(data []byte) []byte {
	if len(data) > 0 && data[len(data)-1] == 0 {
		return data[:len(data)-1]
	}
	return data
}

// deviceID 生成设备 ID
func (s *Socket) deviceID() ([]byte, error) {
	device, err := s.client.FindDeviceBySerial(s.serial)
	if err != nil {
		return nil, fmt.Errorf("failed to get device: %w", err)
	}

	deviceInfo := device.DeviceInfo()
	id := fmt.Sprintf(
		"device::ro.product.name=%s;ro.product.model=%s;ro.product.device=%s;",
		deviceInfo.Product,
		deviceInfo.Model,
		deviceInfo.Device,
	)

	return append([]byte(id), 0), nil
}

// handleAuthPacket 处理认证数据包
func (s *Socket) handleAuthPacket(packet *Packet) error {
	switch packet.Arg0 {
	case AuthSignature:
		// 存储签名
		if len(packet.Data) > 0 {
			if s.signature == nil {
				s.signature = make([]byte, len(packet.Data))
				copy(s.signature, packet.Data)
			}
		}

		// 检查已知公钥
		if s.options.socketOptions.KnownPublicKeys != nil {
			digest := string(s.token)
			sig := string(s.signature)

			for _, key := range s.options.socketOptions.KnownPublicKeys {
				if key.Verify(digest, sig) {
					return s.acceptConnection()
				}
			}
		}

		// 重新发送认证令牌
		return s.SendPacket(NewPacket(CommandOfAUTH, AuthToken, 0, s.token))
	case AuthRSAPublicKey:
		if s.signature == nil {
			return NewAuthError("Public key sent before signature")
		}

		if packet.Data == nil || len(packet.Data) < 2 {
			return NewAuthError("Empty RSA public key")
		}

		// 解析公钥（简化实现）
		keyData := s.skipNull(packet.Data)
		key := NewPublicKey(string(keyData))

		// 验证签名
		digest := string(s.token)
		sig := string(s.signature)

		if !key.Verify(digest, sig) {
			return NewAuthError("Signature mismatch")
		}

		// 调用用户定义的认证函数
		if s.options.socketOptions.Auth != nil {
			if err := s.options.socketOptions.Auth(key); err != nil {
				return NewAuthError("Rejected by user-defined handler")
			}
		}

		return s.acceptConnection()
	default:
		return fmt.Errorf("unknown authentication method: %d", packet.Arg0)
	}
}

// acceptConnection 接受连接
func (s *Socket) acceptConnection() error {
	deviceID, err := s.deviceID()
	if err != nil {
		return fmt.Errorf("failed to get device ID: %w", err)
	}

	s.authorized = true

	// 发送连接确认
	return s.SendPacket(NewPacket(CommandOfCNXN, Swap32(s.version), s.maxPayload, deviceID))
}

// handleOpenPacket 处理Open数据包
func (s *Socket) handleOpenPacket(packet *Packet) error {
	if !s.authorized {
		return NewUnauthorizedError()
	}

	remoteID := packet.Arg0
	localID := s.remoteID.Next()

	if packet.Data == nil || len(packet.Data) < 2 {
		return fmt.Errorf("empty service name")
	}

	_ = s.skipNull(packet.Data) // 服务名称，在简化实现中暂不使用

	// 创建服务
	service := NewService(s.client, s.serial, localID, remoteID, s)

	// 插入服务映射
	if err := s.services.Insert(localID, service); err != nil {
		return fmt.Errorf("failed to insert service: %w", err)
	}

	// 处理打开请求
	if err := service.Handle(packet); err != nil {
		return fmt.Errorf("failed to handle service packet: %w", err)
	}

	return nil
}

// forwardServicePacket 转发服务数据包
func (s *Socket) forwardServicePacket(packet *Packet) error {
	if !s.authorized {
		return NewUnauthorizedError()
	}

	localID := packet.Arg1
	service := s.services.Get(localID)
	if service == nil {
		// 服务可能已经关闭，忽略数据包
		return nil
	}

	defer func() {
		if packet.Command == CommandOfCLSE {
			s.services.Remove(localID)
		}
	}()

	return service.Handle(packet)
}
